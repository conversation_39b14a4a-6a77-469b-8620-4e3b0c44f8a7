{"ast": null, "code": "var _s = $RefreshSig$();\n// Firebase real-time listener hook\nimport { useEffect, useCallback } from 'react';\nimport { useAppDispatch } from '../../../app/store';\nimport { setPlayers, setCurrentQuestion, setScores, setRound2Grid, setRound4Grid } from '../../../app/store/slices/gameSlice';\nimport { setCurrentRoom, setPlayers as setRoomPlayers } from '../../../app/store/slices/roomSlice';\nimport { firebaseRealtimeService } from '../../services/firebase/realtime';\nimport { FirebaseRoomListener } from '../../../services/firebaseServices';\nexport const useFirebaseListener = roomId => {\n  _s();\n  const dispatch = useAppDispatch();\n  const listener = FirebaseRoomListener.getInstance(roomId || '');\n  /**\r\n   * Listen to room data changes\r\n   */\n  const listenToRoom = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRoom(roomId, data => {\n      if (data) {\n        // Update room state\n        dispatch(setCurrentRoom(data));\n\n        // Call optional callback\n        callback === null || callback === void 0 ? void 0 : callback(data);\n      }\n    });\n  }, [roomId, dispatch]);\n  const listenToTimeStart = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToTimeStart(callback);\n  }, [roomId, dispatch]);\n  const listenToSound = useCallback(callback => {\n    if (!roomId) return () => {};\n    return listener.listenToSound(callback);\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to player answers\r\n   */\n  const listenToPlayerAnswers = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToPlayerAnswers(roomId, answers => {\n      // Convert to array and update Redux state\n      const playersArray = Object.values(answers);\n      dispatch(setPlayers(playersArray));\n      dispatch(setRoomPlayers(playersArray.map(p => ({\n        ...p,\n        joinedAt: '',\n        isReady: true,\n        isConnected: true,\n        role: 'player'\n      }))));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(answers);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to current question\r\n   */\n  const listenToCurrentQuestion = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToCurrentQuestion(roomId, question => {\n      dispatch(setCurrentQuestion(question));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(question);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to scores\r\n   */\n  const listenToScores = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToScores(roomId, scores => {\n      dispatch(setScores(scores));\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(scores);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to game state\r\n   */\n  const listenToGameState = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToGameState(roomId, state => {\n      if (state) {\n        // Update relevant Redux state based on game state\n        if (state.currentRound) {\n          // dispatch(setCurrentRound(state.currentRound));\n        }\n        if (state.isActive !== undefined) {\n          // dispatch(setIsActive(state.isActive));\n        }\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(state);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to Round 2 grid\r\n   */\n  const listenToRound2Grid = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRound2Grid(roomId, grid => {\n      if (grid) {\n        dispatch(setRound2Grid(grid));\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(grid);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Listen to Round 4 grid\r\n   */\n  const listenToRound4Grid = useCallback(callback => {\n    if (!roomId) return () => {};\n    return firebaseRealtimeService.listenToRound4Grid(roomId, grid => {\n      if (grid) {\n        dispatch(setRound4Grid(grid));\n      }\n\n      // Call optional callback\n      callback === null || callback === void 0 ? void 0 : callback(grid);\n    });\n  }, [roomId, dispatch]);\n\n  /**\r\n   * Setup all listeners at once\r\n   */\n  const setupAllListeners = useCallback(callbacks => {\n    if (!roomId) return () => {};\n    const unsubscribers = [listenToRoom(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRoomChange), listenToPlayerAnswers(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onPlayerAnswersChange), listenToCurrentQuestion(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onQuestionChange), listenToScores(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onScoresChange), listenToGameState(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onGameStateChange), listenToRound2Grid(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRound2GridChange), listenToRound4Grid(callbacks === null || callbacks === void 0 ? void 0 : callbacks.onRound4GridChange)];\n    return () => {\n      unsubscribers.forEach(unsubscribe => unsubscribe());\n    };\n  }, [roomId, listenToRoom, listenToPlayerAnswers, listenToCurrentQuestion, listenToScores, listenToGameState, listenToRound2Grid, listenToRound4Grid]);\n\n  /**\r\n   * Update player data\r\n   */\n  const updatePlayer = useCallback(async (playerId, playerData) => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updatePlayer(roomId, playerId, playerData);\n  }, [roomId]);\n\n  /**\r\n   * Set current question\r\n   */\n  const setCurrentQuestionFirebase = useCallback(async question => {\n    if (!roomId) return;\n    await firebaseRealtimeService.setCurrentQuestion(roomId, question);\n  }, [roomId]);\n\n  /**\r\n   * Update scores\r\n   */\n  const updateScoresFirebase = useCallback(async scores => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updateScores(roomId, scores);\n  }, [roomId]);\n\n  /**\r\n   * Update game state\r\n   */\n  const updateGameStateFirebase = useCallback(async gameState => {\n    if (!roomId) return;\n    await firebaseRealtimeService.updateGameState(roomId, gameState);\n  }, [roomId]);\n\n  /**\r\n   * Cleanup all listeners on unmount\r\n   */\n  useEffect(() => {\n    return () => {\n      firebaseRealtimeService.removeAllListeners();\n    };\n  }, []);\n  return {\n    // Listeners\n    listenToRoom,\n    listenToPlayerAnswers,\n    listenToCurrentQuestion,\n    listenToScores,\n    listenToGameState,\n    listenToRound2Grid,\n    listenToRound4Grid,\n    setupAllListeners,\n    // Writers\n    updatePlayer,\n    setCurrentQuestionFirebase,\n    updateScoresFirebase,\n    updateGameStateFirebase\n  };\n};\n_s(useFirebaseListener, \"e0JI5JCyRJItQBF4xYkeqNYVwyo=\", false, function () {\n  return [useAppDispatch];\n});\nexport default useFirebaseListener;", "map": {"version": 3, "names": ["useEffect", "useCallback", "useAppDispatch", "setPlayers", "setCurrentQuestion", "setScores", "setRound2Grid", "setRound4Grid", "setCurrentRoom", "setRoomPlayers", "firebaseRealtimeService", "FirebaseRoomListener", "useFirebaseListener", "roomId", "_s", "dispatch", "listener", "getInstance", "listenToRoom", "callback", "data", "listenToTimeStart", "listenToSound", "listenToPlayerAnswers", "answers", "players<PERSON><PERSON>y", "Object", "values", "map", "p", "joinedAt", "isReady", "isConnected", "role", "listenToCurrentQuestion", "question", "listenToScores", "scores", "listenToGameState", "state", "currentRound", "isActive", "undefined", "listenToRound2Grid", "grid", "listenToRound4Grid", "setupAllListeners", "callbacks", "unsubscribers", "onRoomChange", "onPlayerAnswersChange", "onQuestionChange", "onScoresChange", "onGameStateChange", "onRound2GridChange", "onRound4GridChange", "for<PERSON>ach", "unsubscribe", "updatePlayer", "playerId", "player<PERSON><PERSON>", "setCurrentQuestionFirebase", "updateScoresFirebase", "updateScores", "updateGameStateFirebase", "gameState", "updateGameState", "removeAllListeners"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/firebase/useFirebaseListener.ts"], "sourcesContent": ["// Firebase real-time listener hook\r\nimport { useEffect, useCallback } from 'react';\r\nimport { useAppDispatch } from '../../../app/store';\r\nimport { \r\n  setPlayers, \r\n  setCurrentQuestion, \r\n  setScores, \r\n  setRound2Grid, \r\n  setRound4Grid \r\n} from '../../../app/store/slices/gameSlice';\r\nimport { \r\n  setCurrentRoom, \r\n  setPlayers as setRoomPlayers \r\n} from '../../../app/store/slices/roomSlice';\r\nimport { firebaseRealtimeService } from '../../services/firebase/realtime';\r\nimport { PlayerData, Question, Score, Room } from '../../types';\r\nimport { FirebaseRoomListener } from '../../../services/firebaseServices';\r\n\r\nexport const useFirebaseListener = (roomId: string | null) => {\r\n  const dispatch = useAppDispatch();\r\n  const listener = FirebaseRoomListener.getInstance(roomId || '');\r\n  /**\r\n   * Listen to room data changes\r\n   */\r\n  const listenToRoom = useCallback((callback?: (data: any) => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return firebaseRealtimeService.listenToRoom(roomId, (data) => {\r\n      if (data) {\r\n        // Update room state\r\n        dispatch(setCurrentRoom(data as Room));\r\n        \r\n        // Call optional callback\r\n        callback?.(data);\r\n      }\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  const listenToTimeStart = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return listener.listenToTimeStart(\r\n      callback\r\n    );\r\n  }, [roomId, dispatch]);\r\n\r\n  const listenToSound = useCallback((callback?: () => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return listener.listenToSound(\r\n      callback\r\n    );\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to player answers\r\n   */\r\n  const listenToPlayerAnswers = useCallback((callback?: (answers: Record<string, PlayerData>) => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return firebaseRealtimeService.listenToPlayerAnswers(roomId, (answers) => {\r\n      // Convert to array and update Redux state\r\n      const playersArray = Object.values(answers);\r\n      dispatch(setPlayers(playersArray));\r\n      dispatch(setRoomPlayers(playersArray.map(p => ({ ...p, joinedAt: '', isReady: true, isConnected: true, role: 'player' as const }))));\r\n      \r\n      // Call optional callback\r\n      callback?.(answers);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to current question\r\n   */\r\n  const listenToCurrentQuestion = useCallback((callback?: (question: Question | null) => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return firebaseRealtimeService.listenToCurrentQuestion(roomId, (question) => {\r\n      dispatch(setCurrentQuestion(question));\r\n      \r\n      // Call optional callback\r\n      callback?.(question);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to scores\r\n   */\r\n  const listenToScores = useCallback((callback?: (scores: Score[]) => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return firebaseRealtimeService.listenToScores(roomId, (scores) => {\r\n      dispatch(setScores(scores));\r\n      \r\n      // Call optional callback\r\n      callback?.(scores);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to game state\r\n   */\r\n  const listenToGameState = useCallback((callback?: (state: any) => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return firebaseRealtimeService.listenToGameState(roomId, (state) => {\r\n      if (state) {\r\n        // Update relevant Redux state based on game state\r\n        if (state.currentRound) {\r\n          // dispatch(setCurrentRound(state.currentRound));\r\n        }\r\n        if (state.isActive !== undefined) {\r\n          // dispatch(setIsActive(state.isActive));\r\n        }\r\n      }\r\n      \r\n      // Call optional callback\r\n      callback?.(state);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to Round 2 grid\r\n   */\r\n  const listenToRound2Grid = useCallback((callback?: (grid: any) => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return firebaseRealtimeService.listenToRound2Grid(roomId, (grid) => {\r\n      if (grid) {\r\n        dispatch(setRound2Grid(grid));\r\n      }\r\n      \r\n      // Call optional callback\r\n      callback?.(grid);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Listen to Round 4 grid\r\n   */\r\n  const listenToRound4Grid = useCallback((callback?: (grid: any) => void) => {\r\n    if (!roomId) return () => {};\r\n\r\n    return firebaseRealtimeService.listenToRound4Grid(roomId, (grid) => {\r\n      if (grid) {\r\n        dispatch(setRound4Grid(grid));\r\n      }\r\n      \r\n      // Call optional callback\r\n      callback?.(grid);\r\n    });\r\n  }, [roomId, dispatch]);\r\n\r\n  /**\r\n   * Setup all listeners at once\r\n   */\r\n  const setupAllListeners = useCallback((callbacks?: {\r\n    onRoomChange?: (data: any) => void;\r\n    onPlayerAnswersChange?: (answers: Record<string, PlayerData>) => void;\r\n    onQuestionChange?: (question: Question | null) => void;\r\n    onScoresChange?: (scores: Score[]) => void;\r\n    onGameStateChange?: (state: any) => void;\r\n    onRound2GridChange?: (grid: any) => void;\r\n    onRound4GridChange?: (grid: any) => void;\r\n  }) => {\r\n    if (!roomId) return () => {};\r\n\r\n    const unsubscribers = [\r\n      listenToRoom(callbacks?.onRoomChange),\r\n      listenToPlayerAnswers(callbacks?.onPlayerAnswersChange),\r\n      listenToCurrentQuestion(callbacks?.onQuestionChange),\r\n      listenToScores(callbacks?.onScoresChange),\r\n      listenToGameState(callbacks?.onGameStateChange),\r\n      listenToRound2Grid(callbacks?.onRound2GridChange),\r\n      listenToRound4Grid(callbacks?.onRound4GridChange),\r\n    ];\r\n\r\n    return () => {\r\n      unsubscribers.forEach(unsubscribe => unsubscribe());\r\n    };\r\n  }, [\r\n    roomId,\r\n    listenToRoom,\r\n    listenToPlayerAnswers,\r\n    listenToCurrentQuestion,\r\n    listenToScores,\r\n    listenToGameState,\r\n    listenToRound2Grid,\r\n    listenToRound4Grid,\r\n  ]);\r\n\r\n  /**\r\n   * Update player data\r\n   */\r\n  const updatePlayer = useCallback(async (playerId: string, playerData: Partial<PlayerData>) => {\r\n    if (!roomId) return;\r\n    \r\n    await firebaseRealtimeService.updatePlayer(roomId, playerId, playerData);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Set current question\r\n   */\r\n  const setCurrentQuestionFirebase = useCallback(async (question: Question) => {\r\n    if (!roomId) return;\r\n    \r\n    await firebaseRealtimeService.setCurrentQuestion(roomId, question);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Update scores\r\n   */\r\n  const updateScoresFirebase = useCallback(async (scores: Score[]) => {\r\n    if (!roomId) return;\r\n    \r\n    await firebaseRealtimeService.updateScores(roomId, scores);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Update game state\r\n   */\r\n  const updateGameStateFirebase = useCallback(async (gameState: any) => {\r\n    if (!roomId) return;\r\n    \r\n    await firebaseRealtimeService.updateGameState(roomId, gameState);\r\n  }, [roomId]);\r\n\r\n  /**\r\n   * Cleanup all listeners on unmount\r\n   */\r\n  useEffect(() => {\r\n    return () => {\r\n      firebaseRealtimeService.removeAllListeners();\r\n    };\r\n  }, []);\r\n\r\n  return {\r\n    // Listeners\r\n    listenToRoom,\r\n    listenToPlayerAnswers,\r\n    listenToCurrentQuestion,\r\n    listenToScores,\r\n    listenToGameState,\r\n    listenToRound2Grid,\r\n    listenToRound4Grid,\r\n    setupAllListeners,\r\n    \r\n    // Writers\r\n    updatePlayer,\r\n    setCurrentQuestionFirebase,\r\n    updateScoresFirebase,\r\n    updateGameStateFirebase,\r\n  };\r\n};\r\n\r\nexport default useFirebaseListener;\r\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SACEC,UAAU,EACVC,kBAAkB,EAClBC,SAAS,EACTC,aAAa,EACbC,aAAa,QACR,qCAAqC;AAC5C,SACEC,cAAc,EACdL,UAAU,IAAIM,cAAc,QACvB,qCAAqC;AAC5C,SAASC,uBAAuB,QAAQ,kCAAkC;AAE1E,SAASC,oBAAoB,QAAQ,oCAAoC;AAEzE,OAAO,MAAMC,mBAAmB,GAAIC,MAAqB,IAAK;EAAAC,EAAA;EAC5D,MAAMC,QAAQ,GAAGb,cAAc,CAAC,CAAC;EACjC,MAAMc,QAAQ,GAAGL,oBAAoB,CAACM,WAAW,CAACJ,MAAM,IAAI,EAAE,CAAC;EAC/D;AACF;AACA;EACE,MAAMK,YAAY,GAAGjB,WAAW,CAAEkB,QAA8B,IAAK;IACnE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOH,uBAAuB,CAACQ,YAAY,CAACL,MAAM,EAAGO,IAAI,IAAK;MAC5D,IAAIA,IAAI,EAAE;QACR;QACAL,QAAQ,CAACP,cAAc,CAACY,IAAY,CAAC,CAAC;;QAEtC;QACAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGC,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACP,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAEtB,MAAMM,iBAAiB,GAAGpB,WAAW,CAAEkB,QAAqB,IAAK;IAC/D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOG,QAAQ,CAACK,iBAAiB,CAC/BF,QACF,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAEtB,MAAMO,aAAa,GAAGrB,WAAW,CAAEkB,QAAqB,IAAK;IAC3D,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOG,QAAQ,CAACM,aAAa,CAC3BH,QACF,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMQ,qBAAqB,GAAGtB,WAAW,CAAEkB,QAAwD,IAAK;IACtG,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOH,uBAAuB,CAACa,qBAAqB,CAACV,MAAM,EAAGW,OAAO,IAAK;MACxE;MACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC;MAC3CT,QAAQ,CAACZ,UAAU,CAACsB,YAAY,CAAC,CAAC;MAClCV,QAAQ,CAACN,cAAc,CAACgB,YAAY,CAACG,GAAG,CAACC,CAAC,KAAK;QAAE,GAAGA,CAAC;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE,IAAI;QAAEC,WAAW,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpI;MACAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGK,OAAO,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACX,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMmB,uBAAuB,GAAGjC,WAAW,CAAEkB,QAA8C,IAAK;IAC9F,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOH,uBAAuB,CAACwB,uBAAuB,CAACrB,MAAM,EAAGsB,QAAQ,IAAK;MAC3EpB,QAAQ,CAACX,kBAAkB,CAAC+B,QAAQ,CAAC,CAAC;;MAEtC;MACAhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGgB,QAAQ,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtB,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMqB,cAAc,GAAGnC,WAAW,CAAEkB,QAAoC,IAAK;IAC3E,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOH,uBAAuB,CAAC0B,cAAc,CAACvB,MAAM,EAAGwB,MAAM,IAAK;MAChEtB,QAAQ,CAACV,SAAS,CAACgC,MAAM,CAAC,CAAC;;MAE3B;MACAlB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGkB,MAAM,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxB,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMuB,iBAAiB,GAAGrC,WAAW,CAAEkB,QAA+B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOH,uBAAuB,CAAC4B,iBAAiB,CAACzB,MAAM,EAAG0B,KAAK,IAAK;MAClE,IAAIA,KAAK,EAAE;QACT;QACA,IAAIA,KAAK,CAACC,YAAY,EAAE;UACtB;QAAA;QAEF,IAAID,KAAK,CAACE,QAAQ,KAAKC,SAAS,EAAE;UAChC;QAAA;MAEJ;;MAEA;MACAvB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGoB,KAAK,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1B,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM4B,kBAAkB,GAAG1C,WAAW,CAAEkB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOH,uBAAuB,CAACiC,kBAAkB,CAAC9B,MAAM,EAAG+B,IAAI,IAAK;MAClE,IAAIA,IAAI,EAAE;QACR7B,QAAQ,CAACT,aAAa,CAACsC,IAAI,CAAC,CAAC;MAC/B;;MAEA;MACAzB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGyB,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/B,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM8B,kBAAkB,GAAG5C,WAAW,CAAEkB,QAA8B,IAAK;IACzE,IAAI,CAACN,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,OAAOH,uBAAuB,CAACmC,kBAAkB,CAAChC,MAAM,EAAG+B,IAAI,IAAK;MAClE,IAAIA,IAAI,EAAE;QACR7B,QAAQ,CAACR,aAAa,CAACqC,IAAI,CAAC,CAAC;MAC/B;;MAEA;MACAzB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGyB,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/B,MAAM,EAAEE,QAAQ,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAM+B,iBAAiB,GAAG7C,WAAW,CAAE8C,SAQtC,IAAK;IACJ,IAAI,CAAClC,MAAM,EAAE,OAAO,MAAM,CAAC,CAAC;IAE5B,MAAMmC,aAAa,GAAG,CACpB9B,YAAY,CAAC6B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,YAAY,CAAC,EACrC1B,qBAAqB,CAACwB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,qBAAqB,CAAC,EACvDhB,uBAAuB,CAACa,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEI,gBAAgB,CAAC,EACpDf,cAAc,CAACW,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEK,cAAc,CAAC,EACzCd,iBAAiB,CAACS,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEM,iBAAiB,CAAC,EAC/CV,kBAAkB,CAACI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,kBAAkB,CAAC,EACjDT,kBAAkB,CAACE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,kBAAkB,CAAC,CAClD;IAED,OAAO,MAAM;MACXP,aAAa,CAACQ,OAAO,CAACC,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CACD5C,MAAM,EACNK,YAAY,EACZK,qBAAqB,EACrBW,uBAAuB,EACvBE,cAAc,EACdE,iBAAiB,EACjBK,kBAAkB,EAClBE,kBAAkB,CACnB,CAAC;;EAEF;AACF;AACA;EACE,MAAMa,YAAY,GAAGzD,WAAW,CAAC,OAAO0D,QAAgB,EAAEC,UAA+B,KAAK;IAC5F,IAAI,CAAC/C,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACgD,YAAY,CAAC7C,MAAM,EAAE8C,QAAQ,EAAEC,UAAU,CAAC;EAC1E,CAAC,EAAE,CAAC/C,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMgD,0BAA0B,GAAG5D,WAAW,CAAC,MAAOkC,QAAkB,IAAK;IAC3E,IAAI,CAACtB,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACN,kBAAkB,CAACS,MAAM,EAAEsB,QAAQ,CAAC;EACpE,CAAC,EAAE,CAACtB,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMiD,oBAAoB,GAAG7D,WAAW,CAAC,MAAOoC,MAAe,IAAK;IAClE,IAAI,CAACxB,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACqD,YAAY,CAAClD,MAAM,EAAEwB,MAAM,CAAC;EAC5D,CAAC,EAAE,CAACxB,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMmD,uBAAuB,GAAG/D,WAAW,CAAC,MAAOgE,SAAc,IAAK;IACpE,IAAI,CAACpD,MAAM,EAAE;IAEb,MAAMH,uBAAuB,CAACwD,eAAe,CAACrD,MAAM,EAAEoD,SAAS,CAAC;EAClE,CAAC,EAAE,CAACpD,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACEb,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXU,uBAAuB,CAACyD,kBAAkB,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL;IACAjD,YAAY;IACZK,qBAAqB;IACrBW,uBAAuB;IACvBE,cAAc;IACdE,iBAAiB;IACjBK,kBAAkB;IAClBE,kBAAkB;IAClBC,iBAAiB;IAEjB;IACAY,YAAY;IACZG,0BAA0B;IAC1BC,oBAAoB;IACpBE;EACF,CAAC;AACH,CAAC;AAAClD,EAAA,CA3OWF,mBAAmB;EAAA,QACbV,cAAc;AAAA;AA4OjC,eAAeU,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}